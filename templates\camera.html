<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Detection - Fabric Defect Detection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .camera-container {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }
        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        #videoElement {
            width: 100%;
            height: auto;
        }
        .camera-controls {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 0 0 10px 10px;
        }
        .stats-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .defect-indicator {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            text-align: center;
        }
        .confidence-slider {
            width: 100%;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-search"></i> Fabric Defect Detection
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Image Upload</a>
                <a class="nav-link active" href="/camera">Camera</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-camera"></i> Real-time Camera Detection</h5>
                    </div>
                    <div class="card-body">
                        <div class="camera-container">
                            <div class="video-container">
                                <video id="videoElement" autoplay muted></video>
                                <div class="stats-overlay" id="statsOverlay">
                                    <div>FPS: <span id="fpsDisplay">0</span></div>
                                    <div>Current Defects: <span id="defectsDisplay">0</span></div>
                                    <div>Conf: <span id="confDisplay">0.40</span></div>
                                </div>
                            </div>
                            <div class="camera-controls">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary" id="startBtn" onclick="startCamera()">
                                            <i class="fas fa-play"></i> Start Camera
                                        </button>
                                        <button class="btn btn-danger" id="stopBtn" onclick="stopCamera()" disabled>
                                            <i class="fas fa-stop"></i> Stop Camera
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="detectionToggle" checked>
                                            <label class="form-check-label text-white" for="detectionToggle">
                                                Enable Detection
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <label for="confidenceSlider" class="form-label text-white">
                                        Confidence Threshold: <span id="confidenceValue">0.4</span>
                                    </label>
                                    <input type="range" class="form-range confidence-slider" 
                                           id="confidenceSlider" min="0.05" max="0.95" step="0.05" value="0.4">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Live Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>Camera Status</h6>
                            <div>
                                <span class="status-indicator" id="cameraStatus"></span>
                                <span id="cameraStatusText">Disconnected</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>Detection Status</h6>
                            <div>
                                <span class="status-indicator" id="detectionStatus"></span>
                                <span id="detectionStatusText">Inactive</span>
                            </div>
                        </div>

                        <div id="defectCounts">
                            <!-- Individual defect counts will be populated here -->
                        </div>

                        <div class="mt-3">
                            <h6>Recent Detections</h6>
                            <div id="recentDetections">
                                <p class="text-muted">No detections yet</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let stream = null;
        let detectionInterval = null;
        let fpsCounter = 0;
        let fpsStartTime = Date.now();
        let currentFps = 0;

        const videoElement = document.getElementById('videoElement');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const confidenceSlider = document.getElementById('confidenceSlider');
        const confidenceValue = document.getElementById('confidenceValue');
        const detectionToggle = document.getElementById('detectionToggle');

        // Update confidence threshold
        confidenceSlider.addEventListener('input', (e) => {
            confidenceValue.textContent = e.target.value;
            updateConfidence(e.target.value);
        });

        async function updateConfidence(confidence) {
            try {
                const response = await fetch('/update_confidence', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ confidence: parseFloat(confidence) })
                });
                const data = await response.json();
                if (data.success) {
                    console.log('Confidence updated:', data.confidence);
                }
            } catch (error) {
                console.error('Error updating confidence:', error);
            }
        }

        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 1280 }, 
                        height: { ideal: 720 } 
                    } 
                });
                
                videoElement.srcObject = stream;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
                updateCameraStatus(true);
                
                // Start detection if enabled
                if (detectionToggle.checked) {
                    startDetection();
                }
                
                // Start FPS calculation
                startFpsCalculation();
                
            } catch (error) {
                console.error('Error accessing camera:', error);
                alert('Failed to access camera. Please check permissions.');
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            
            videoElement.srcObject = null;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            
            updateCameraStatus(false);
            stopDetection();
            stopFpsCalculation();
        }

        function startDetection() {
            if (detectionInterval) return;
            
            detectionInterval = setInterval(async () => {
                if (!detectionToggle.checked) return;
                
                try {
                    // Capture frame from video
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.width = videoElement.videoWidth;
                    canvas.height = videoElement.videoHeight;
                    context.drawImage(videoElement, 0, 0);
                    
                    // Convert to blob and send for detection
                    canvas.toBlob(async (blob) => {
                        const formData = new FormData();
                        formData.append('image', blob);
                        
                        try {
                            const response = await fetch('/detect', {
                                method: 'POST',
                                body: formData
                            });

                            if (!response.ok) {
                                throw new Error(`Server error: ${response.status}`);
                            }

                            const data = await response.json();
                            if (data.success) {
                                updateDetectionStats(data);
                            }
                        } catch (error) {
                            console.error('Detection error:', error);
                            if (error.message.includes('Server error') || error.message.includes('Failed to fetch')) {
                                // Show warning once
                                if (!window.backendWarningShown) {
                                    alert('⚠️ Backend Not Available\n\nCamera detection requires a Python backend. This static demo cannot process images.\n\nFor full functionality, deploy to Vercel, Railway, or run locally.');
                                    window.backendWarningShown = true;
                                }
                                // Stop detection to avoid repeated errors
                                document.getElementById('detectionToggle').checked = false;
                                stopDetection();
                            }
                        }
                    }, 'image/jpeg');
                    
                } catch (error) {
                    console.error('Frame capture error:', error);
                }
            }, 1000); // Detect every second
            
            updateDetectionStatus(true);
        }

        function stopDetection() {
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
            updateDetectionStatus(false);
        }

        function startFpsCalculation() {
            setInterval(() => {
                const now = Date.now();
                const elapsed = (now - fpsStartTime) / 1000;
                currentFps = Math.round(fpsCounter / elapsed);
                
                document.getElementById('fpsDisplay').textContent = currentFps;
                
                fpsCounter = 0;
                fpsStartTime = now;
            }, 1000);
        }

        function stopFpsCalculation() {
            document.getElementById('fpsDisplay').textContent = '0';
            currentFps = 0;
        }

        function updateCameraStatus(connected) {
            const statusIndicator = document.getElementById('cameraStatus');
            const statusText = document.getElementById('cameraStatusText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'Connected';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Disconnected';
            }
        }

        function updateDetectionStatus(active) {
            const statusIndicator = document.getElementById('detectionStatus');
            const statusText = document.getElementById('detectionStatusText');
            
            if (active) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'Active';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Inactive';
            }
        }

        function updateDetectionStats(data) {
            // Update current defects count
            document.getElementById('defectsDisplay').textContent = data.total_defects;
            
            // Update defect counts
            updateDefectCounts(data.defect_counts);
            
            // Update recent detections
            updateRecentDetections(data.detections);
        }

        function updateDefectCounts(defectCounts) {
            const container = document.getElementById('defectCounts');
            container.innerHTML = '';

            for (const [defectType, count] of Object.entries(defectCounts)) {
                const defectDiv = document.createElement('div');
                defectDiv.className = 'defect-indicator';
                defectDiv.innerHTML = `
                    <strong>${defectType.charAt(0).toUpperCase() + defectType.slice(1)}: ${count}</strong>
                `;
                container.appendChild(defectDiv);
            }
        }

        function updateRecentDetections(detections) {
            const container = document.getElementById('recentDetections');
            
            if (detections.length === 0) {
                container.innerHTML = '<p class="text-muted">No detections</p>';
                return;
            }

            container.innerHTML = '';
            detections.slice(0, 5).forEach((det, index) => {
                const detDiv = document.createElement('div');
                detDiv.className = 'border-bottom pb-2 mb-2';
                detDiv.innerHTML = `
                    <strong>${det.class}</strong><br>
                    <small class="text-muted">Conf: ${(det.confidence * 100).toFixed(1)}%</small>
                `;
                container.appendChild(detDiv);
            });
        }

        // Detection toggle handler
        detectionToggle.addEventListener('change', (e) => {
            if (e.target.checked && stream) {
                startDetection();
            } else {
                stopDetection();
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            stopCamera();
        });
    </script>
</body>
</html>
