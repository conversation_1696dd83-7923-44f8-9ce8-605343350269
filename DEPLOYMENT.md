# Deployment Guide for Fabric Defect Detection App

This guide covers multiple deployment options for the Fabric Defect Detection application.

## 🚀 Quick Deployment Options

### 1. Heroku Deployment

#### Prerequisites
- Heroku account
- Heroku CLI installed

#### Steps
1. **Create Heroku App**
   ```bash
   heroku create your-app-name
   ```

2. **Set up GitHub Secrets**
   Go to your GitHub repository → Settings → Secrets and variables → Actions
   Add these secrets:
   - `HEROKU_API_KEY`: Your Heroku API key
   - `HEROKU_APP_NAME`: Your Heroku app name

3. **Deploy**
   - Push to main branch
   - GitHub Actions will automatically deploy

#### Manual Deployment
```bash
# Login to Heroku
heroku login

# Create app
heroku create fabric-defect-detection

# Set buildpack
heroku buildpacks:set heroku/python

# Deploy
git push heroku main
```

### 2. Alternative Deployment Options

For other deployment platforms, you can use the standard Python deployment methods:

#### Google Cloud Run
```bash
# Build and submit to Cloud Build
gcloud builds submit --tag gcr.io/PROJECT-ID/fabric-defect-detection

# Deploy to Cloud Run
gcloud run deploy --image gcr.io/PROJECT-ID/fabric-defect-detection --platform managed
```

#### AWS Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Initialize and deploy
eb init
eb create fabric-defect-detection
eb deploy
```

## 🔧 Configuration

### Environment Variables
- `PORT`: Port number (default: 5000)
- `FLASK_ENV`: Environment (development/production)
- `MODEL_PATH`: Path to YOLOv8 model file

### Required Files
- `requirements.txt`: Python dependencies
- `Dockerfile`: Container configuration
- `Procfile`: Heroku process configuration
- `railway.json`: Railway configuration

## 📊 Monitoring and Health Checks

### Health Check Endpoint
- URL: `/health`
- Returns: Application status and model information

### Logs
Monitor application logs through your deployment platform:
- Heroku: `heroku logs --tail`
- Railway: Check logs in dashboard
- Docker: `docker logs container-name`

## 🔒 Security Considerations

### Production Settings
1. Set `FLASK_ENV=production`
2. Use environment variables for sensitive data
3. Enable HTTPS in production
4. Set up proper CORS if needed

### File Upload Security
- Validate file types
- Limit file sizes (currently 16MB)
- Scan uploaded files if needed

## 🚨 Troubleshooting

### Common Issues

1. **Model Loading Errors**
   - Ensure `models/best.pt` exists
   - Check file permissions
   - Verify model compatibility

2. **Memory Issues**
   - Increase container memory limits
   - Use CPU-only inference for smaller instances
   - Consider model optimization

3. **Port Binding Issues**
   - Ensure PORT environment variable is set correctly
   - Check if port is available

4. **Dependency Issues**
   - Update requirements.txt
   - Use specific package versions
   - Check Python version compatibility

### Performance Optimization

1. **Model Optimization**
   - Use TensorRT for GPU inference
   - Quantize model for smaller size
   - Cache model predictions

2. **Application Optimization**
   - Use gunicorn for production
   - Enable gzip compression
   - Implement caching

## 📝 GitHub Actions Workflow

The included workflow (`.github/workflows/deploy.yml`) provides:
- Automated testing
- Multi-platform deployment
- Docker image building
- Error handling and notifications

### Workflow Features
- ✅ Code linting with flake8
- ✅ Application startup testing
- ✅ Heroku deployment
- ✅ Railway deployment
- ✅ Docker Hub publishing
- ✅ Parallel deployment jobs

## 🎯 Next Steps

1. **Set up monitoring** (e.g., Sentry, DataDog)
2. **Configure CI/CD** with the provided workflow
3. **Set up domain** and SSL certificates
4. **Implement user authentication** if needed
5. **Add database** for storing results
6. **Scale horizontally** with load balancers

## 📞 Support

For deployment issues:
- Check application logs
- Review GitHub Actions workflow runs
- Consult platform-specific documentation
- Contact: <EMAIL>
