{"name": "Fabric Defect Detection", "description": "AI-powered fabric defect detection web application using YOLOv8 and Flask", "repository": "https://github.com/yourusername/fabric-defect-detection", "logo": "https://raw.githubusercontent.com/yourusername/fabric-defect-detection/main/static/logo.png", "keywords": ["ai", "machine-learning", "yolo", "fabric", "defect-detection", "computer-vision", "flask", "python"], "image": "heroku/python", "stack": "heroku-22", "buildpacks": [{"url": "heroku/python"}], "env": {"FLASK_ENV": {"description": "Flask environment", "value": "production"}, "MODEL_PATH": {"description": "Path to the YOLOv8 model file", "value": "./models/best.pt"}}, "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [], "scripts": {"postdeploy": "echo 'Fabric Defect Detection app deployed successfully!'"}, "environments": {"test": {"scripts": {"test": "python -m pytest"}}}}