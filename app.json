{"name": "Fabric Defect Detection", "description": "AI-powered fabric defect detection web application using YOLOv8 and Flask", "repository": "https://github.com/LOVEPOISON11/fabric-defect-detection", "keywords": ["ai", "machine-learning", "yolo", "fabric", "defect-detection", "computer-vision", "flask", "python"], "stack": "heroku-22", "buildpacks": [{"url": "heroku/python"}], "env": {"FLASK_ENV": {"description": "Flask environment", "value": "production"}, "MODEL_PATH": {"description": "Path to the YOLOv8 model file", "value": "./models/best.pt"}}, "formation": {"web": {"quantity": 1, "size": "eco"}}, "addons": [], "scripts": {"postdeploy": "echo 'Fabric Defect Detection app deployed successfully! Visit your app URL to start detecting fabric defects.'"}}