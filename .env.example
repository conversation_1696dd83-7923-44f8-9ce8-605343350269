# Flask Configuration
FLASK_ENV=development
FLASK_APP=app.py
PORT=5000

# Model Configuration
MODEL_PATH=./models/best.pt

# Upload Configuration
MAX_CONTENT_LENGTH=16777216

# Security (set in production)
SECRET_KEY=your-secret-key-here

# Deployment Platform Specific
# Heroku
HEROKU_API_KEY=your-heroku-api-key
HEROKU_APP_NAME=your-app-name

# Railway
RAILWAY_TOKEN=your-railway-token
RAILWAY_SERVICE_NAME=fabric-defect-detection

# Docker Hub
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password
