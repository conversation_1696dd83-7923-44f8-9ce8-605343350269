<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric Defect Detection - AI-Powered Quality Control</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .demo-section {
            background-color: #f8f9fa;
            padding: 80px 0;
        }
        .tech-badge {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-search"></i> Fabric Defect Detection</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#features">Features</a></li>
                    <li class="nav-item"><a class="nav-link" href="#demo">Demo</a></li>
                    <li class="nav-item"><a class="nav-link" href="#tech">Technology</a></li>
                    <li class="nav-item"><a class="nav-link" href="#deploy">Deploy</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">AI-Powered Fabric Defect Detection</h1>
            <p class="lead mb-5">Advanced computer vision technology using YOLOv8 to detect and classify fabric defects in real-time</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="#demo" class="btn btn-light btn-lg me-md-2"><i class="fas fa-play"></i> View Demo</a>
                        <a href="https://github.com/LOVEPOISON11/fabric-defect-detection" class="btn btn-outline-light btn-lg" target="_blank">
                            <i class="fab fa-github"></i> View Code
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="display-5 fw-bold">Key Features</h2>
                    <p class="lead text-muted">Comprehensive fabric quality control solution</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Image Upload Detection</h5>
                            <p class="card-text">Upload fabric images and get instant defect detection results with bounding boxes and confidence scores.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-video fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Real-time Camera Detection</h5>
                            <p class="card-text">Use your webcam for live fabric defect monitoring with real-time FPS and defect statistics.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-cog fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Adjustable Confidence</h5>
                            <p class="card-text">Fine-tune detection sensitivity with adjustable confidence thresholds for optimal results.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="demo-section">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="display-5 fw-bold">Live Demo</h2>
                    <p class="lead text-muted">Experience the power of AI-driven fabric defect detection</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow-lg">
                        <div class="card-body p-5 text-center">
                            <i class="fas fa-rocket fa-4x text-primary mb-4"></i>
                            <h4 class="mb-3">Deploy Your Own Instance</h4>
                            <p class="mb-4">The full Flask application with YOLOv8 model needs to be deployed on a platform that supports Python applications.</p>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="https://heroku.com/deploy?template=https://github.com/LOVEPOISON11/fabric-defect-detection" 
                                   class="btn btn-primary btn-lg me-md-2" target="_blank">
                                    <i class="fas fa-cloud"></i> Deploy to Heroku
                                </a>
                                <a href="https://github.com/LOVEPOISON11/fabric-defect-detection" 
                                   class="btn btn-outline-primary btn-lg" target="_blank">
                                    <i class="fab fa-github"></i> View Source
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section id="tech" class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="display-5 fw-bold">Technology Stack</h2>
                    <p class="lead text-muted">Built with cutting-edge AI and web technologies</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="text-center">
                        <span class="tech-badge"><i class="fab fa-python"></i> Python 3.11</span>
                        <span class="tech-badge"><i class="fas fa-flask"></i> Flask</span>
                        <span class="tech-badge"><i class="fas fa-brain"></i> YOLOv8</span>
                        <span class="tech-badge"><i class="fas fa-eye"></i> OpenCV</span>
                        <span class="tech-badge"><i class="fab fa-bootstrap"></i> Bootstrap 5</span>
                        <span class="tech-badge"><i class="fab fa-js"></i> JavaScript</span>
                        <span class="tech-badge"><i class="fab fa-docker"></i> Gunicorn</span>
                        <span class="tech-badge"><i class="fab fa-github"></i> GitHub Actions</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Deployment Section -->
    <section id="deploy" class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="display-5 fw-bold">Easy Deployment</h2>
                    <p class="lead text-muted">Get your fabric defect detection system running in minutes</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body p-4">
                            <h5 class="card-title"><i class="fas fa-cloud text-primary"></i> Heroku Deployment</h5>
                            <p class="card-text">One-click deployment to Heroku with automatic scaling and HTTPS.</p>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Free tier available</li>
                                <li><i class="fas fa-check text-success"></i> Automatic HTTPS</li>
                                <li><i class="fas fa-check text-success"></i> Easy scaling</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body p-4">
                            <h5 class="card-title"><i class="fab fa-github text-dark"></i> GitHub Actions CI/CD</h5>
                            <p class="card-text">Automated testing and deployment pipeline with every code push.</p>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Automated testing</li>
                                <li><i class="fas fa-check text-success"></i> Code quality checks</li>
                                <li><i class="fas fa-check text-success"></i> Auto deployment</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-2">&copy; 2024 Fabric Defect Detection. Built with ❤️ for the textile industry.</p>
            <p class="mb-0">
                <a href="https://github.com/LOVEPOISON11/fabric-defect-detection" class="text-white me-3" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
                <a href="mailto:<EMAIL>" class="text-white">
                    <i class="fas fa-envelope"></i> Contact
                </a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
