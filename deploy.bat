@echo off
echo.
echo ========================================
echo   Fabric Defect Detection - Deploy
echo ========================================
echo.

echo Checking Git status...
git --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/
    pause
    exit /b 1
)

echo.
echo Current project status:
git status --porcelain

echo.
echo Adding all files to Git...
git add .

echo.
echo Committing changes...
git commit -m "Add deployment configuration and workflow

- Complete CI/CD pipeline with GitHub Actions
- Docker containerization
- Multi-platform deployment support (Heroku, Railway)
- Production-ready configuration
- Health check endpoints
- Automated testing and deployment"

echo.
echo ========================================
echo   Deployment Options
echo ========================================
echo.
echo 1. Push to GitHub (then deploy via platform)
echo 2. Show deployment instructions
echo 3. Exit
echo.
set /p choice="Choose option (1-3): "

if "%choice%"=="1" (
    echo.
    set /p github_username="Enter your GitHub username: "
    if "!github_username!"=="" (
        echo ERROR: GitHub username is required
        pause
        exit /b 1
    )
    
    echo.
    echo Setting up GitHub remote...
    git remote remove origin 2>nul
    git remote add origin https://github.com/!github_username!/fabric-defect-detection.git
    
    echo.
    echo Pushing to GitHub...
    git branch -M main
    git push -u origin main
    
    echo.
    echo ========================================
    echo   SUCCESS! Code pushed to GitHub
    echo ========================================
    echo.
    echo Your repository: https://github.com/!github_username!/fabric-defect-detection
    echo.
    echo Next steps:
    echo 1. Go to your GitHub repository
    echo 2. Enable GitHub Actions if prompted
    echo 3. Choose a deployment platform:
    echo.
    echo   HEROKU: https://dashboard.heroku.com/new-app
    echo   - Connect GitHub repository
    echo   - Enable automatic deploys
    echo.
    echo   RAILWAY: https://railway.app/
    echo   - Deploy from GitHub repo
    echo   - Select your repository
    echo.
    echo For detailed instructions, see: DEPLOY_NOW.md
    echo.
    
) else if "%choice%"=="2" (
    echo.
    echo Opening deployment instructions...
    start DEPLOY_NOW.md
    
) else if "%choice%"=="3" (
    echo Goodbye!
    exit /b 0
    
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Contact for help: <EMAIL>
echo.
pause
