# GitHub Repository Setup Guide

Follow these steps to set up your GitHub repository and enable automated deployment.

## 📋 Prerequisites

1. **GitHub Account**: Make sure you have a GitHub account
2. **Git Installed**: Install Git on your local machine
3. **Repository Access**: You should have push access to the repository

## 🚀 Step-by-Step Setup

### 1. Initialize Git Repository (if not already done)

```bash
# Navigate to your project directory
cd fabric-defect-detection

# Initialize git (if not already initialized)
git init

# Add all files
git add .

# Make initial commit
git commit -m "Initial commit: Fabric Defect Detection App with deployment workflow"
```

### 2. Create GitHub Repository

1. Go to [GitHub](https://github.com)
2. Click "New repository" or go to https://github.com/new
3. Repository name: `fabric-defect-detection`
4. Description: `AI-powered fabric defect detection web application using YOLOv8`
5. Make it **Public** (for free GitHub Actions minutes)
6. Don't initialize with README (we already have files)
7. Click "Create repository"

### 3. Connect Local Repository to GitHub

```bash
# Add GitHub remote (replace YOUR_USERNAME with your GitHub username)
git remote add origin https://github.com/YOUR_USERNAME/fabric-defect-detection.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### 4. Set Up GitHub Secrets

Go to your repository on GitHub → Settings → Secrets and variables → Actions

Add these secrets for deployment:

#### For Heroku Deployment:
- **Name**: `HEROKU_API_KEY`
  - **Value**: Your Heroku API key (get from Heroku dashboard → Account settings → API Key)
- **Name**: `HEROKU_APP_NAME`
  - **Value**: Your Heroku app name (e.g., `my-fabric-detector`)

#### Optional: For future deployment platforms:
You can add more secrets later if you want to deploy to other platforms.

### 5. Enable GitHub Actions

1. Go to your repository → Actions tab
2. GitHub will automatically detect the workflow file
3. Click "I understand my workflows, go ahead and enable them"

### 6. Test the Deployment

1. Make a small change to any file (e.g., update README.md)
2. Commit and push:
   ```bash
   git add .
   git commit -m "Test deployment workflow"
   git push origin main
   ```
3. Go to Actions tab to see the workflow running

## 🔧 Platform-Specific Setup

### Heroku Setup

1. **Create Heroku Account**: https://signup.heroku.com/
2. **Install Heroku CLI**: https://devcenter.heroku.com/articles/heroku-cli
3. **Create App**:
   ```bash
   heroku create your-app-name
   ```
4. **Get API Key**: Heroku Dashboard → Account Settings → API Key

### Other Platform Setup

For other deployment platforms, refer to their respective documentation:
- **Google Cloud Run**: https://cloud.google.com/run/docs
- **AWS Elastic Beanstalk**: https://docs.aws.amazon.com/elasticbeanstalk/
- **DigitalOcean App Platform**: https://docs.digitalocean.com/products/app-platform/

## 📊 Monitoring Deployments

### GitHub Actions
- Go to repository → Actions tab
- Click on workflow runs to see logs
- Check for any errors in deployment

### Platform Dashboards
- **Heroku**: https://dashboard.heroku.com/apps

## 🚨 Troubleshooting

### Common Issues

1. **Workflow Fails**
   - Check GitHub Actions logs
   - Verify all secrets are set correctly
   - Ensure repository has correct permissions

2. **Heroku Deployment Fails**
   - Check Heroku app logs: `heroku logs --tail -a your-app-name`
   - Verify Procfile is correct
   - Check buildpack settings

3. **Model Loading Issues**
   - Ensure `models/best.pt` is in repository
   - Check file size limits (GitHub: 100MB, Git LFS for larger files)
   - Verify model path in code

4. **Docker Build Fails**
   - Check Dockerfile syntax
   - Verify all dependencies in requirements.txt
   - Test build locally first

### Getting Help

1. **Check Logs**: Always check deployment logs first
2. **GitHub Issues**: Create issues in your repository
3. **Platform Documentation**: 
   - [Heroku Dev Center](https://devcenter.heroku.com/)
   - [Railway Docs](https://docs.railway.app/)
   - [Docker Documentation](https://docs.docker.com/)

## ✅ Success Checklist

- [ ] Repository created on GitHub
- [ ] Local code pushed to GitHub
- [ ] GitHub Actions enabled
- [ ] Deployment secrets configured
- [ ] First workflow run successful
- [ ] Application accessible via deployment URL
- [ ] Health check endpoint working (`/health`)

## 🎯 Next Steps

1. **Custom Domain**: Set up custom domain for your app
2. **SSL Certificate**: Enable HTTPS
3. **Monitoring**: Set up application monitoring
4. **Database**: Add database for storing results
5. **Authentication**: Implement user authentication if needed

## 📞 Contact

For setup assistance: <EMAIL>

---

**Repository URL Template**: `https://github.com/YOUR_USERNAME/fabric-defect-detection`

Replace `YOUR_USERNAME` with your actual GitHub username.
