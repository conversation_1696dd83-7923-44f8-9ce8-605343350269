name: Deploy Flask App to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Create build directory
      run: |
        mkdir -p dist
        
    - name: Copy project files
      run: |
        # Copy templates
        cp -r templates dist/ || echo "No templates directory"
        
        # Copy static files if they exist
        cp -r static dist/ 2>/dev/null || mkdir -p dist/static
        
        # Copy Python files
        cp *.py dist/ 2>/dev/null || echo "No Python files in root"
        
        # Copy models directory
        cp -r models dist/ 2>/dev/null || echo "No models directory"
        
        # Copy uploads directory structure
        mkdir -p dist/uploads
        
        # Copy requirements.txt
        cp requirements.txt dist/ 2>/dev/null || echo "No requirements.txt"
        
        # Copy README
        cp README.md dist/ 2>/dev/null || echo "No README.md"
        
    - name: Create simple index page
      run: |
        cat > dist/index.html << 'EOF'
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Fabric Defect Detection</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container py-5">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-body text-center p-5">
                                <i class="fas fa-microscope fa-4x text-primary mb-4"></i>
                                <h1 class="display-5 fw-bold mb-3">Fabric Defect Detection</h1>
                                <p class="lead mb-4">AI-powered fabric quality inspection system using YOLOv8</p>
                                
                                <div class="row g-3 mb-4">
                                    <div class="col-md-6">
                                        <a href="templates/index.html" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-upload"></i> Image Upload
                                        </a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="templates/camera.html" class="btn btn-info btn-lg w-100">
                                            <i class="fas fa-camera"></i> Camera Detection
                                        </a>
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Note:</strong> This is a static deployment. For full functionality, 
                                    run locally with Python or deploy to a platform supporting Flask.
                                </div>
                                
                                <a href="https://github.com/LOVEPOISON11/fabric-defect-detection" class="btn btn-outline-dark">
                                    <i class="fab fa-github"></i> View Source Code
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        EOF
        
    - name: Setup Pages
      uses: actions/configure-pages@v4
      
    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: ./dist

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
