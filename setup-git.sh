#!/bin/bash

# Fabric Defect Detection - Git Setup Script
# This script helps set up the GitHub repository

echo "🚀 Fabric Defect Detection - GitHub Setup"
echo "=========================================="

# Check if git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Get GitHub username
read -p "Enter your GitHub username: " GITHUB_USERNAME

if [ -z "$GITHUB_USERNAME" ]; then
    echo "❌ GitHub username is required"
    exit 1
fi

# Repository name
REPO_NAME="fabric-defect-detection"
REPO_URL="https://github.com/$GITHUB_USERNAME/$REPO_NAME.git"

echo ""
echo "📋 Repository Details:"
echo "Username: $GITHUB_USERNAME"
echo "Repository: $REPO_NAME"
echo "URL: $REPO_URL"
echo ""

# Confirm setup
read -p "Continue with setup? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "Setup cancelled."
    exit 0
fi

echo ""
echo "🔧 Setting up Git repository..."

# Initialize git if not already done
if [ ! -d ".git" ]; then
    echo "Initializing Git repository..."
    git init
else
    echo "Git repository already initialized."
fi

# Add all files
echo "Adding files to Git..."
git add .

# Check if there are changes to commit
if git diff --staged --quiet; then
    echo "No changes to commit."
else
    # Commit changes
    echo "Committing changes..."
    git commit -m "Initial commit: Fabric Defect Detection App with deployment workflow

Features:
- YOLOv8-based fabric defect detection
- Flask web application
- Docker containerization
- GitHub Actions CI/CD pipeline
- Multi-platform deployment support (Heroku, Railway, Docker Hub)
- Health check endpoints
- Production-ready configuration"
fi

# Add remote origin
echo "Adding GitHub remote..."
if git remote get-url origin &> /dev/null; then
    echo "Remote origin already exists. Updating..."
    git remote set-url origin $REPO_URL
else
    git remote add origin $REPO_URL
fi

# Set main branch
echo "Setting up main branch..."
git branch -M main

echo ""
echo "✅ Git setup complete!"
echo ""
echo "📝 Next Steps:"
echo "1. Create repository on GitHub: https://github.com/new"
echo "   - Repository name: $REPO_NAME"
echo "   - Make it public for free GitHub Actions"
echo "   - Don't initialize with README"
echo ""
echo "2. Push to GitHub:"
echo "   git push -u origin main"
echo ""
echo "3. Set up GitHub Secrets (Repository → Settings → Secrets):"
echo "   - HEROKU_API_KEY (for Heroku deployment)"
echo "   - HEROKU_APP_NAME (for Heroku deployment)"
echo "   - RAILWAY_TOKEN (for Railway deployment)"
echo "   - DOCKER_USERNAME (for Docker Hub)"
echo "   - DOCKER_PASSWORD (for Docker Hub)"
echo ""
echo "4. Enable GitHub Actions in your repository"
echo ""
echo "📖 For detailed instructions, see: setup-github.md"
echo ""
echo "🌐 Your repository will be available at:"
echo "   $REPO_URL"
echo ""
echo "📧 Contact: <EMAIL>"
